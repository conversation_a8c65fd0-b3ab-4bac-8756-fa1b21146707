// Frida script to hook GoodyHutHelperConfig class (Unity IL2CPP Native)
// Target: Dominations game (com.nexonm.dominations.adk)

console.log("[*] GoodyHutHelperConfig Native IL2CPP Hook Script Started");

// Step 1: Locate and attach to libil2cpp.so
try {
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so not found");
    } else {
        console.log("[+] libil2cpp.so base address: " + il2cpp.base);
    }
} catch (e) {
    console.log("[-] Error finding libil2cpp.so: " + e.message);
}

// Global variables to track GoodyHutHelperConfig methods
var goodyHutMethods = {};
var methodCallCount = 0;

// Step 2: Hook IL2CPP runtime functions to intercept method calls
function hookIL2CPPRuntime() {
    console.log("[*] Setting up IL2CPP runtime hooks...");

    // Hook il2cpp_runtime_invoke to catch all method invocations
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_runtime_invoke = il2cpp.findExportByName("il2cpp_runtime_invoke");
    if (il2cpp_runtime_invoke) {
        console.log("[+] Found il2cpp_runtime_invoke at: " + il2cpp_runtime_invoke);

        Interceptor.attach(il2cpp_runtime_invoke, {
            onEnter: function (args) {
                // args[0] = MethodInfo pointer
                // args[1] = object instance pointer
                // args[2] = parameters array
                // args[3] = exception pointer

                this.methodInfo = args[0];
                this.instance = args[1];
                this.params = args[2];

                // Get method name
                var methodName = getMethodName(this.methodInfo);
                if (methodName && methodName.indexOf("GoodyHutHelperConfig") !== -1) {
                    methodCallCount++;
                    console.log("[*] GoodyHutHelperConfig method called (#" + methodCallCount + ")");
                    console.log("    Method: " + methodName);
                    console.log("    Instance: " + this.instance);
                    console.log("    MethodInfo: " + this.methodInfo);

                    // Store method info for later use
                    if (!goodyHutMethods[methodName]) {
                        goodyHutMethods[methodName] = this.methodInfo;
                        console.log("[+] Stored method: " + methodName);
                    }

                    // Log instance data if this is a constructor
                    if (methodName.indexOf(".ctor") !== -1) {
                        console.log("[*] Constructor called - will log instance after completion");
                        this.isConstructor = true;
                    }

                    // Log property access
                    if (methodName.indexOf("get_") !== -1 || methodName.indexOf("set_") !== -1) {
                        this.isProperty = true;
                        this.propertyName = methodName.replace("get_", "").replace("set_", "");
                    }
                }
            },
            onLeave: function (retval) {
                if (this.methodInfo && goodyHutMethods[getMethodName(this.methodInfo)]) {
                    var methodName = getMethodName(this.methodInfo);

                    if (this.isConstructor) {
                        console.log("[*] Constructor completed, logging instance...");
                        setTimeout(function() {
                            logGoodyHutHelperConfigInstance(this.instance);
                        }.bind(this), 100);
                    }

                    if (this.isProperty) {
                        if (methodName.indexOf("get_") !== -1) {
                            console.log("[*] Property getter '" + this.propertyName + "' returned: " + retval);
                        } else if (methodName.indexOf("set_") !== -1) {
                            console.log("[*] Property setter '" + this.propertyName + "' completed");
                        }
                    }

                    console.log("    Return value: " + retval);
                }
            }
        });
    } else {
        console.log("[-] il2cpp_runtime_invoke not found");
    }
}

// Step 3: Hook method name resolution to identify GoodyHutHelperConfig methods
function hookMethodNameResolution() {
    console.log("[*] Setting up method name resolution hooks...");

    // Hook il2cpp_method_get_name to identify GoodyHutHelperConfig methods
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_method_get_name = il2cpp.findExportByName("il2cpp_method_get_name");
    if (il2cpp_method_get_name) {
        console.log("[+] Found il2cpp_method_get_name at: " + il2cpp_method_get_name);

        Interceptor.attach(il2cpp_method_get_name, {
            onLeave: function (retval) {
                if (retval && !retval.isNull()) {
                    var methodName = retval.readCString();
                    if (methodName && methodName.indexOf("GoodyHutHelperConfig") !== -1) {
                        console.log("[+] Found GoodyHutHelperConfig method: " + methodName);
                    }
                }
            }
        });
    } else {
        console.log("[-] il2cpp_method_get_name not found");
    }

    // Hook il2cpp_class_get_name to identify GoodyHutHelperConfig class
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_class_get_name = il2cpp.findExportByName("il2cpp_class_get_name");
    if (il2cpp_class_get_name) {
        console.log("[+] Found il2cpp_class_get_name at: " + il2cpp_class_get_name);

        Interceptor.attach(il2cpp_class_get_name, {
            onLeave: function (retval) {
                if (retval && !retval.isNull()) {
                    var className = retval.readCString();
                    if (className && className.indexOf("GoodyHutHelperConfig") !== -1) {
                        console.log("[+] Found GoodyHutHelperConfig class: " + className);
                    }
                }
            }
        });
    } else {
        console.log("[-] il2cpp_class_get_name not found");
    }
}
// Helper function to get method name from MethodInfo pointer
function getMethodName(methodInfoPtr) {
    if (!methodInfoPtr || methodInfoPtr.isNull()) {
        return null;
    }

    try {
        var il2cpp = Process.findModuleByName("libil2cpp.so");
        if (il2cpp) {
            var il2cpp_method_get_name = il2cpp.findExportByName("il2cpp_method_get_name");
            if (il2cpp_method_get_name) {
                var namePtr = new NativeFunction(il2cpp_method_get_name, 'pointer', ['pointer'])(methodInfoPtr);
            if (namePtr && !namePtr.isNull()) {
                return namePtr.readCString();
            }
        }
    } catch (e) {
        // Fallback: try to read method name directly from memory structure
        try {
            // MethodInfo structure typically has name at offset 0x10
            var namePtr = methodInfoPtr.add(0x10).readPointer();
            if (namePtr && !namePtr.isNull()) {
                return namePtr.readCString();
            }
        } catch (e2) {
            // If all else fails, return null
        }
    }

    return null;
}

// Function to log GoodyHutHelperConfig instance details using memory reading
function logGoodyHutHelperConfigInstance(instancePtr) {
    if (!instancePtr || instancePtr.isNull()) {
        console.log("[-] Invalid instance pointer");
        return;
    }

    console.log("=== GoodyHutHelperConfig Instance ===");
    console.log("[*] Instance pointer: " + instancePtr);

    try {
        // Read memory values directly from the instance based on C# structure
        // From the provided C# code:
        // Offset 0x10: fuzzedMaxExp (FuzzedInt)
        // Offset 0x18: fuzzedNumCitizens (FuzzedInt)
        // Offset 0x20: ageRequirement (int)
        // Offset 0x28: rampingRewards (List<RampingReward>)
        // Offset 0x30: cleanUp (bool)
        // Offset 0x34: fuzzedSellPrice (FuzzedInt)
        // Offset 0x3C: sellResource (TagEnum)
        // Offset 0x40: maxHealth (int)
        // Offset 0x44: bonusCollectible (TagEnum)
        // Offset 0x48: fuzzedBonusAmount (FuzzedInt)
        // Offset 0x50: isMystery (bool)
        // Offset 0x51: hasWeights (bool)
        // Offset 0x54: fixedRewardValue (int)

        console.log("=== Memory Layout Analysis ===");

        // Try to read basic integer fields
        try {
            var ageRequirement = instancePtr.add(0x20).readS32();
            console.log("ageRequirement (0x20): " + ageRequirement);
        } catch (e) { console.log("ageRequirement: read error - " + e.message); }

        try {
            var cleanUp = instancePtr.add(0x30).readU8();
            console.log("cleanUp (0x30): " + (cleanUp ? "true" : "false"));
        } catch (e) { console.log("cleanUp: read error - " + e.message); }

        try {
            var sellResource = instancePtr.add(0x3C).readS32();
            console.log("sellResource (0x3C): " + sellResource);
        } catch (e) { console.log("sellResource: read error - " + e.message); }

        try {
            var maxHealth = instancePtr.add(0x40).readS32();
            console.log("maxHealth (0x40): " + maxHealth);
        } catch (e) { console.log("maxHealth: read error - " + e.message); }

        try {
            var bonusCollectible = instancePtr.add(0x44).readS32();
            console.log("bonusCollectible (0x44): " + bonusCollectible);
        } catch (e) { console.log("bonusCollectible: read error - " + e.message); }

        try {
            var isMystery = instancePtr.add(0x50).readU8();
            console.log("isMystery (0x50): " + (isMystery ? "true" : "false"));
        } catch (e) { console.log("isMystery: read error - " + e.message); }

        try {
            var hasWeights = instancePtr.add(0x51).readU8();
            console.log("hasWeights (0x51): " + (hasWeights ? "true" : "false"));
        } catch (e) { console.log("hasWeights: read error - " + e.message); }

        try {
            var fixedRewardValue = instancePtr.add(0x54).readS32();
            console.log("fixedRewardValue (0x54): " + fixedRewardValue);
        } catch (e) { console.log("fixedRewardValue: read error - " + e.message); }

        // Try to read FuzzedInt structures (they contain the actual int value)
        try {
            // FuzzedInt typically stores the actual value at offset 0x0 within the structure
            var maxExpPtr = instancePtr.add(0x10);
            var maxExplorations = maxExpPtr.readS32();
            console.log("maxExplorations (0x10): " + maxExplorations);
        } catch (e) { console.log("maxExplorations: read error - " + e.message); }

        try {
            var numCitizensPtr = instancePtr.add(0x18);
            var numCitizens = numCitizensPtr.readS32();
            console.log("numCitizens (0x18): " + numCitizens);
        } catch (e) { console.log("numCitizens: read error - " + e.message); }

        try {
            var sellPricePtr = instancePtr.add(0x34);
            var sellPrice = sellPricePtr.readS32();
            console.log("sellPrice (0x34): " + sellPrice);
        } catch (e) { console.log("sellPrice: read error - " + e.message); }

        try {
            var bonusAmountPtr = instancePtr.add(0x48);
            var bonusAmount = bonusAmountPtr.readS32();
            console.log("bonusAmount (0x48): " + bonusAmount);
        } catch (e) { console.log("bonusAmount: read error - " + e.message); }

        // Try to read List<RampingReward> at offset 0x28
        try {
            var rampingRewardsPtr = instancePtr.add(0x28).readPointer();
            if (rampingRewardsPtr && !rampingRewardsPtr.isNull()) {
                console.log("rampingRewards pointer (0x28): " + rampingRewardsPtr);
                // List structure typically has count at offset 0x18
                var count = rampingRewardsPtr.add(0x18).readS32();
                console.log("rampingRewards count: " + count);
            }
        } catch (e) { console.log("rampingRewards: read error - " + e.message); }

    } catch (e) {
        console.log("[-] Error reading instance data: " + e.message);
    }

    console.log("=====================================");
}

// Initialize the hooks
console.log("[*] Initializing IL2CPP hooks...");

// Wait a bit for the game to load
setTimeout(function() {
    hookIL2CPPRuntime();
    hookMethodNameResolution();
    console.log("[+] All hooks initialized!");
}, 2000);

console.log("[*] GoodyHutHelperConfig Native IL2CPP Hook Script Loaded");
