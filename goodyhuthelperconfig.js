// Frida script to hook GoodyHutHelperConfig class
// Target: Dominations game (com.nexonm.dominations.adk)

console.log("[*] GoodyHutHelperConfig Hook Script Started");

// Wait for the game to load
Java.perform(function() {
    console.log("[*] Java environment ready, searching for GoodyHutHelperConfig class...");
    
    try {
        // Try to find the GoodyHutHelperConfig class
        var GoodyHutHelperConfig = Java.use("GoodyHutHelperConfig");
        console.log("[+] Found GoodyHutHelperConfig class!");
        
        // Hook the constructor
        GoodyHutHelperConfig.$init.overload('BXmlNode').implementation = function(config) {
            console.log("[*] GoodyHutHelperConfig constructor called");
            console.log("[*] Config parameter: " + config);
            
            // Call original constructor
            var result = this.$init(config);
            
            // Log the created instance
            logGoodyHutHelperConfig(this);
            
            return result;
        };
        
        // Hook maxExplorations getter
        GoodyHutHelperConfig.maxExplorations.getter.implementation = function() {
            var result = this.maxExplorations.getter.call(this);
            console.log("[*] maxExplorations getter called, returning: " + result);
            return result;
        };
        
        // Hook maxExplorations setter
        GoodyHutHelperConfig.maxExplorations.setter.implementation = function(value) {
            console.log("[*] maxExplorations setter called with value: " + value);
            return this.maxExplorations.setter.call(this, value);
        };
        
        // Hook numCitizens getter
        GoodyHutHelperConfig.numCitizens.getter.implementation = function() {
            var result = this.numCitizens.getter.call(this);
            console.log("[*] numCitizens getter called, returning: " + result);
            return result;
        };
        
        // Hook numCitizens setter
        GoodyHutHelperConfig.numCitizens.setter.implementation = function(value) {
            console.log("[*] numCitizens setter called with value: " + value);
            return this.numCitizens.setter.call(this, value);
        };
        
        // Hook sellPrice getter
        GoodyHutHelperConfig.sellPrice.getter.implementation = function() {
            var result = this.sellPrice.getter.call(this);
            console.log("[*] sellPrice getter called, returning: " + result);
            return result;
        };
        
        // Hook sellPrice setter
        GoodyHutHelperConfig.sellPrice.setter.implementation = function(value) {
            console.log("[*] sellPrice setter called with value: " + value);
            return this.sellPrice.setter.call(this, value);
        };
        
        // Hook bonusAmount getter
        GoodyHutHelperConfig.bonusAmount.getter.implementation = function() {
            var result = this.bonusAmount.getter.call(this);
            console.log("[*] bonusAmount getter called, returning: " + result);
            return result;
        };
        
        // Hook bonusAmount setter
        GoodyHutHelperConfig.bonusAmount.setter.implementation = function(value) {
            console.log("[*] bonusAmount setter called with value: " + value);
            return this.bonusAmount.setter.call(this, value);
        };
        
        console.log("[+] GoodyHutHelperConfig hooks installed successfully!");
        
    } catch (e) {
        console.log("[-] Error hooking GoodyHutHelperConfig: " + e.message);
        console.log("[-] Trying alternative class names...");
        
        // Try with different possible class names
        var possibleNames = [
            "com.nexonm.dominations.GoodyHutHelperConfig",
            "com.nexonm.dominations.adk.GoodyHutHelperConfig",
            "GoodyHutHelperConfig",
            "com.dominations.GoodyHutHelperConfig"
        ];
        
        for (var i = 0; i < possibleNames.length; i++) {
            try {
                var className = possibleNames[i];
                console.log("[*] Trying class name: " + className);
                var ConfigClass = Java.use(className);
                console.log("[+] Found class with name: " + className);
                hookGoodyHutHelperConfigClass(ConfigClass);
                break;
            } catch (e2) {
                console.log("[-] Class not found: " + className);
            }
        }
    }
    
    // Function to log GoodyHutHelperConfig instance details
    function logGoodyHutHelperConfig(instance) {
        console.log("=== GoodyHutHelperConfig Instance ===");
        
        try {
            // Log basic properties
            console.log("maxExplorations: " + instance.maxExplorations.value);
            console.log("numCitizens: " + instance.numCitizens.value);
            console.log("sellPrice: " + instance.sellPrice.value);
            console.log("bonusAmount: " + instance.bonusAmount.value);
            
            // Log other fields if accessible
            try {
                console.log("ageRequirement: " + instance.ageRequirement.value);
            } catch (e) { console.log("ageRequirement: not accessible"); }
            
            try {
                console.log("cleanUp: " + instance.cleanUp.value);
            } catch (e) { console.log("cleanUp: not accessible"); }
            
            try {
                console.log("sellResource: " + instance.sellResource.value);
            } catch (e) { console.log("sellResource: not accessible"); }
            
            try {
                console.log("maxHealth: " + instance.maxHealth.value);
            } catch (e) { console.log("maxHealth: not accessible"); }
            
            try {
                console.log("bonusCollectible: " + instance.bonusCollectible.value);
            } catch (e) { console.log("bonusCollectible: not accessible"); }
            
            try {
                console.log("isMystery: " + instance.isMystery.value);
            } catch (e) { console.log("isMystery: not accessible"); }
            
            try {
                console.log("hasWeights: " + instance.hasWeights.value);
            } catch (e) { console.log("hasWeights: not accessible"); }
            
            try {
                console.log("fixedRewardValue: " + instance.fixedRewardValue.value);
            } catch (e) { console.log("fixedRewardValue: not accessible"); }
            
            // Log ramping rewards if accessible
            try {
                var rampingRewards = instance.rampingRewards.value;
                console.log("rampingRewards count: " + rampingRewards.size());
                for (var i = 0; i < rampingRewards.size(); i++) {
                    var reward = rampingRewards.get(i);
                    console.log("  Reward " + i + ": exploration=" + reward.exploration.value + 
                               ", type=" + reward.type.value + 
                               ", amount=" + reward.amount.value + 
                               ", time=" + reward.time.value);
                }
            } catch (e) { 
                console.log("rampingRewards: not accessible - " + e.message); 
            }
            
        } catch (e) {
            console.log("[-] Error logging instance details: " + e.message);
        }
        
        console.log("=====================================");
    }
    
    // Generic hook function for any GoodyHutHelperConfig class
    function hookGoodyHutHelperConfigClass(ConfigClass) {
        console.log("[+] Hooking GoodyHutHelperConfig class methods...");
        
        // Hook constructor if available
        try {
            ConfigClass.$init.overload('BXmlNode').implementation = function(config) {
                console.log("[*] GoodyHutHelperConfig constructor called with BXmlNode");
                var result = this.$init(config);
                logGoodyHutHelperConfig(this);
                return result;
            };
        } catch (e) {
            console.log("[-] Could not hook constructor: " + e.message);
        }
        
        // Try to hook property getters/setters
        var properties = ['maxExplorations', 'numCitizens', 'sellPrice', 'bonusAmount'];
        
        for (var i = 0; i < properties.length; i++) {
            var prop = properties[i];
            try {
                // Hook getter
                var originalGetter = ConfigClass[prop].getter;
                if (originalGetter) {
                    ConfigClass[prop].getter.implementation = function() {
                        var result = originalGetter.call(this);
                        console.log("[*] " + prop + " getter called, returning: " + result);
                        return result;
                    };
                }
                
                // Hook setter
                var originalSetter = ConfigClass[prop].setter;
                if (originalSetter) {
                    ConfigClass[prop].setter.implementation = function(value) {
                        console.log("[*] " + prop + " setter called with value: " + value);
                        return originalSetter.call(this, value);
                    };
                }
            } catch (e) {
                console.log("[-] Could not hook property " + prop + ": " + e.message);
            }
        }
        
        console.log("[+] GoodyHutHelperConfig hooks installed!");
    }
    
    // Also try to find and log all classes containing "GoodyHut"
    console.log("[*] Searching for all GoodyHut related classes...");
    Java.enumerateLoadedClasses({
        onMatch: function(className) {
            if (className.toLowerCase().indexOf("goodyhut") !== -1) {
                console.log("[+] Found GoodyHut related class: " + className);
            }
        },
        onComplete: function() {
            console.log("[*] Class enumeration complete");
        }
    });
});

console.log("[*] GoodyHutHelperConfig Hook Script Loaded");
