// Frida script to hook GoodyHutHelperConfig class (Unity IL2CPP Native)
// Target: Dominations game (com.nexonm.dominations.adk)

console.log("[*] GoodyHutHelperConfig Native IL2CPP Hook Script Started");

// Step 1: Locate and attach to libil2cpp.so
try {
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so not found");
    } else {
        console.log("[+] libil2cpp.so base address: " + il2cpp.base);
    }
} catch (e) {
    console.log("[-] Error finding libil2cpp.so: " + e.message);
}

// Global variables to track GoodyHutHelperConfig methods
var goodyHutMethods = {};
var methodCallCount = 0;
var logThrottleCount = 0;
var autoCollectEnabled = true; // Enable automatic collection features
var lastLogTime = 0;
var debugMode = false; // Set to true for verbose debugging, false for production use
var unityEngineSpamFilter = true; // Filter out Unity engine spam

// Enhanced method detection - comprehensive list of GoodyHutHelperConfig methods
var targetMethods = [
    ".ctor", "Config", "GetJobType", "NumCitizensConsumed", "GetJobSite",
    "IsJobComplete", "CanBuyThrough", "DoJobBuyThrough", "DoFreeBuyThrough",
    "CanBeAttacked", "FillActionIconDataList", "FixedUpdate", "Update",
    "CanCollect", "StartCollect", "FinishCollect", "FinishCollectSpecial",
    "CancelExplore", "SellRuins", "OnAddedToSimulation", "OnMoveStart",
    "OnMoveComplete", "Reset", "GetJobTimeLeft", "GetCooldownTime",
    "GetCooldownTimeLeft", "GetCollectTime", "GetExplorations", "GetRewardType",
    "GetRewardAmount", "IsFixedAmount", "GetHealth", "GetMaxHealth",
    "UpdateHealth", "IsDead", "GetIcon", "InitOnceOnCreateNew",
    "FillInfoBarDataList", "CanBeSelected", "Select", "Unselect",
    "GetSaveObject", "LoadObject", "OnNewWorldWakeup",
    "get_maxExplorations", "set_maxExplorations", "get_numCitizens", "set_numCitizens",
    "get_sellPrice", "set_sellPrice", "get_bonusAmount", "set_bonusAmount"
];

// FuzzingCoefficients for FuzzedInt decryption (from C# code)
var fuzzingCoefficients = {
    XOR1: 0x2C8A8A5E, // 747148286U
    XOR2: 0xD5F5F5EA  // 3588698634U
};

// Step 2: Hook IL2CPP runtime functions to intercept method calls
function hookIL2CPPRuntime() {
    console.log("[*] Setting up IL2CPP runtime hooks...");

    // Hook il2cpp_runtime_invoke to catch all method invocations
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_runtime_invoke = il2cpp.findExportByName("il2cpp_runtime_invoke");
    if (il2cpp_runtime_invoke) {
        console.log("[+] Found il2cpp_runtime_invoke at: " + il2cpp_runtime_invoke);

        Interceptor.attach(il2cpp_runtime_invoke, {
            onEnter: function (args) {
                // args[0] = MethodInfo pointer
                // args[1] = object instance pointer
                // args[2] = parameters array
                // args[3] = exception pointer

                this.methodInfo = args[0];
                this.instance = args[1];
                this.params = args[2];

                // Get method name and class name
                var methodName = getMethodName(this.methodInfo);
                var className = getClassName(this.methodInfo);

                // STRICT class filtering - ONLY GoodyHutHelperConfig methods
                var isGoodyHutMethod = false;

                // Primary check: Must be GoodyHutHelperConfig class
                if (className && className === "GoodyHutHelperConfig") {
                    // Secondary check: Method name must be in our target list
                    if (methodName) {
                        for (var i = 0; i < targetMethods.length; i++) {
                            if (methodName === targetMethods[i]) {
                                isGoodyHutMethod = true;
                                break;
                            }
                        }
                    }
                }

                // Debug mode: Log rejected methods for troubleshooting
                if (debugMode && !isGoodyHutMethod && className && methodName) {
                    // Only log potential candidates to avoid spam
                    if (className.indexOf("Goody") !== -1 ||
                        className.indexOf("Helper") !== -1 ||
                        className.indexOf("Config") !== -1) {
                        console.log("[DEBUG] Rejected method: " + className + "::" + methodName);
                    }
                }

                // Unity engine spam filter
                if (unityEngineSpamFilter && className && methodName) {
                    var unitySpamClasses = ["IsoBody", "UIRoot", "UIRect", "UITweener", "UIPanel",
                                           "UIWidget", "UILabel", "UISprite", "Transform", "GameObject"];
                    var unitySpamMethods = ["FixedUpdate", "Update", "LateUpdate", "OnEnable", "OnDisable"];

                    for (var i = 0; i < unitySpamClasses.length; i++) {
                        if (className.indexOf(unitySpamClasses[i]) !== -1) {
                            for (var j = 0; j < unitySpamMethods.length; j++) {
                                if (methodName === unitySpamMethods[j]) {
                                    // This is Unity engine spam, ignore it
                                    return;
                                }
                            }
                        }
                    }
                }

                if (isGoodyHutMethod) {
                    methodCallCount++;

                    // Performance optimization - throttle logging
                    var currentTime = Date.now();
                    var shouldLog = debugMode || (currentTime - lastLogTime > 500) || methodCallCount % 5 === 0;

                    if (shouldLog) {
                        console.log("[+] GoodyHutHelperConfig method intercepted (#" + methodCallCount + ")");
                        console.log("    Class: " + className);
                        console.log("    Method: " + methodName);
                        console.log("    Instance: " + this.instance);
                        if (debugMode) {
                            console.log("    MethodInfo: " + this.methodInfo);
                        }
                        lastLogTime = currentTime;
                    } else {
                        // Brief logging for non-debug mode
                        if (methodCallCount === 1 || methodCallCount % 50 === 0) {
                            console.log("[+] GoodyHutHelperConfig activity: " + methodCallCount + " methods intercepted");
                        }
                    }

                    // Store method info for later use
                    var fullMethodName = className + "::" + methodName;
                    if (!goodyHutMethods[fullMethodName]) {
                        goodyHutMethods[fullMethodName] = this.methodInfo;
                        console.log("[+] Stored method: " + fullMethodName);
                    }

                    // Enhanced method-specific handling
                    this.methodName = methodName;
                    this.className = className;

                    // Log instance data if this is a constructor
                    if (methodName && methodName.indexOf(".ctor") !== -1) {
                        console.log("[*] Constructor called - will log instance after completion");
                        this.isConstructor = true;
                        // Log constructor parameters
                        logMethodParameters(this.params, methodName);
                    }

                    // Log property access
                    if (methodName && (methodName.indexOf("get_") !== -1 || methodName.indexOf("set_") !== -1)) {
                        this.isProperty = true;
                        this.propertyName = methodName.replace("get_", "").replace("set_", "");
                    }

                    // Handle collection methods for automation
                    if (autoCollectEnabled && methodName) {
                        if (methodName.indexOf("CanCollect") !== -1) {
                            this.isCanCollect = true;
                        } else if (methodName.indexOf("StartCollect") !== -1) {
                            console.log("[*] StartCollect called - automatic collection triggered");
                        } else if (methodName.indexOf("FinishCollect") !== -1) {
                            console.log("[*] FinishCollect called - collection completed");
                        }
                    }

                    // Log parameters for important methods
                    if (methodName && shouldLog) {
                        logMethodParameters(this.params, methodName);
                    }
                }
            },
            onLeave: function (retval) {
                if (this.methodName && this.className) {
                    var currentTime = Date.now();
                    var shouldLog = (currentTime - lastLogTime > 100) || methodCallCount % 10 === 0;

                    if (this.isConstructor) {
                        console.log("[*] Constructor completed, logging instance...");
                        setTimeout(function() {
                            logGoodyHutHelperConfigInstance(this.instance);
                        }.bind(this), 100);
                    }

                    if (this.isProperty && shouldLog) {
                        if (this.methodName.indexOf("get_") !== -1) {
                            console.log("[*] Property getter '" + this.propertyName + "' returned: " + retval);
                        } else if (this.methodName.indexOf("set_") !== -1) {
                            console.log("[*] Property setter '" + this.propertyName + "' completed");
                        }
                    }

                    // Handle automatic collection logic
                    if (autoCollectEnabled && this.isCanCollect && retval) {
                        var canCollectResult = retval.toInt32();
                        if (canCollectResult === 1) { // true
                            console.log("[+] CanCollect returned true - triggering automatic collection");
                            setTimeout(function() {
                                triggerAutoCollection(this.instance);
                            }.bind(this), 500);
                        }
                    }

                    // Enhanced return value logging for important methods
                    if (shouldLog && this.methodName) {
                        if (this.methodName.indexOf("GetRewardAmount") !== -1 ||
                            this.methodName.indexOf("GetRewardType") !== -1 ||
                            this.methodName.indexOf("GetCollectTime") !== -1 ||
                            this.methodName.indexOf("GetExplorations") !== -1) {
                            console.log("    [IMPORTANT] " + this.methodName + " returned: " + retval);
                        } else if (shouldLog) {
                            console.log("    Return value: " + retval);
                        }
                    }
                }
            }
        });
    } else {
        console.log("[-] il2cpp_runtime_invoke not found");
    }
}

// Step 3: Hook method name resolution to identify GoodyHutHelperConfig methods
function hookMethodNameResolution() {
    console.log("[*] Setting up method name resolution hooks...");

    // Hook il2cpp_method_get_name to identify GoodyHutHelperConfig methods
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_method_get_name = il2cpp.findExportByName("il2cpp_method_get_name");
    if (il2cpp_method_get_name) {
        console.log("[+] Found il2cpp_method_get_name at: " + il2cpp_method_get_name);

        Interceptor.attach(il2cpp_method_get_name, {
            onLeave: function (retval) {
                if (retval && !retval.isNull()) {
                    var methodName = retval.readCString();
                    if (methodName && methodName.indexOf("GoodyHutHelperConfig") !== -1) {
                        console.log("[+] Found GoodyHutHelperConfig method: " + methodName);
                    }
                }
            }
        });
    } else {
        console.log("[-] il2cpp_method_get_name not found");
    }

    // Hook il2cpp_class_get_name to identify GoodyHutHelperConfig class
    var il2cpp = Process.findModuleByName("libil2cpp.so");
    if (!il2cpp) {
        console.log("[-] libil2cpp.so module not found");
        return;
    }
    var il2cpp_class_get_name = il2cpp.findExportByName("il2cpp_class_get_name");
    if (il2cpp_class_get_name) {
        console.log("[+] Found il2cpp_class_get_name at: " + il2cpp_class_get_name);

        Interceptor.attach(il2cpp_class_get_name, {
            onLeave: function (retval) {
                if (retval && !retval.isNull()) {
                    var className = retval.readCString();
                    if (className && className.indexOf("GoodyHutHelperConfig") !== -1) {
                        console.log("[+] Found GoodyHutHelperConfig class: " + className);
                    }
                }
            }
        });
    } else {
        console.log("[-] il2cpp_class_get_name not found");
    }
}
// Helper function to get method name from MethodInfo pointer
function getMethodName(methodInfoPtr) {
    if (!methodInfoPtr || methodInfoPtr.isNull()) {
        return null;
    }

    try {
        var il2cpp = Process.findModuleByName("libil2cpp.so");
        if (il2cpp) {
            var il2cpp_method_get_name = il2cpp.findExportByName("il2cpp_method_get_name");
            if (il2cpp_method_get_name) {
                var namePtr = new NativeFunction(il2cpp_method_get_name, 'pointer', ['pointer'])(methodInfoPtr);
                if (namePtr && !namePtr.isNull()) {
                    return namePtr.readCString();
                }
            }
        }
    } catch (e) {
        // Fallback: try to read method name directly from memory structure
        try {
            // MethodInfo structure typically has name at offset 0x10
            var namePtr = methodInfoPtr.add(0x10).readPointer();
            if (namePtr && !namePtr.isNull()) {
                return namePtr.readCString();
            }
        } catch (e2) {
            // If all else fails, return null
        }
    }

    return null;
}

// Enhanced helper function to get class name from MethodInfo pointer with validation
function getClassName(methodInfoPtr) {
    if (!methodInfoPtr || methodInfoPtr.isNull()) {
        return null;
    }

    try {
        var il2cpp = Process.findModuleByName("libil2cpp.so");
        if (il2cpp) {
            var il2cpp_method_get_class = il2cpp.findExportByName("il2cpp_method_get_class");
            var il2cpp_class_get_name = il2cpp.findExportByName("il2cpp_class_get_name");

            if (il2cpp_method_get_class && il2cpp_class_get_name) {
                var classPtr = new NativeFunction(il2cpp_method_get_class, 'pointer', ['pointer'])(methodInfoPtr);
                if (classPtr && !classPtr.isNull()) {
                    var namePtr = new NativeFunction(il2cpp_class_get_name, 'pointer', ['pointer'])(classPtr);
                    if (namePtr && !namePtr.isNull()) {
                        var className = namePtr.readCString();

                        // Validate class name - should be a reasonable string
                        if (className && className.length > 0 && className.length < 200) {
                            // Filter out obvious garbage or system classes
                            if (!className.match(/^[a-zA-Z_][a-zA-Z0-9_]*$/)) {
                                if (debugMode) {
                                    console.log("[DEBUG] Invalid class name format: " + className);
                                }
                                return null;
                            }
                            return className;
                        }
                    }
                }
            }
        }
    } catch (e) {
        if (debugMode) {
            console.log("[DEBUG] Error in getClassName (primary): " + e.message);
        }
    }

    // Enhanced fallback with multiple offset attempts
    try {
        // Try different common offsets for MethodInfo structure
        var possibleOffsets = [0x18, 0x10, 0x08, 0x20];

        for (var i = 0; i < possibleOffsets.length; i++) {
            try {
                var classPtr = methodInfoPtr.add(possibleOffsets[i]).readPointer();
                if (classPtr && !classPtr.isNull()) {
                    // Try different offsets for class name within class structure
                    var nameOffsets = [0x10, 0x08, 0x18, 0x0C];

                    for (var j = 0; j < nameOffsets.length; j++) {
                        try {
                            var namePtr = classPtr.add(nameOffsets[j]).readPointer();
                            if (namePtr && !namePtr.isNull()) {
                                var className = namePtr.readCString();
                                if (className && className.length > 0 && className.length < 200) {
                                    // Basic validation
                                    if (className.match(/^[a-zA-Z_][a-zA-Z0-9_]*$/)) {
                                        if (debugMode) {
                                            console.log("[DEBUG] Found class name via fallback: " + className +
                                                       " (method offset: 0x" + possibleOffsets[i].toString(16) +
                                                       ", name offset: 0x" + nameOffsets[j].toString(16) + ")");
                                        }
                                        return className;
                                    }
                                }
                            }
                        } catch (e3) {
                            // Continue trying other offsets
                        }
                    }
                }
            } catch (e2) {
                // Continue trying other offsets
            }
        }
    } catch (e) {
        if (debugMode) {
            console.log("[DEBUG] Error in getClassName (fallback): " + e.message);
        }
    }

    return null;
}

// Function to log method parameters
function logMethodParameters(paramsPtr, methodName) {
    if (!paramsPtr || paramsPtr.isNull()) {
        return;
    }

    try {
        console.log("[*] Method parameters for " + methodName + ":");

        // For constructor with BXmlNode parameter
        if (methodName && methodName.indexOf(".ctor") !== -1) {
            try {
                var bxmlNodePtr = paramsPtr.readPointer();
                if (bxmlNodePtr && !bxmlNodePtr.isNull()) {
                    console.log("    BXmlNode parameter: " + bxmlNodePtr);
                    // Try to read some BXmlNode data if possible
                    logBXmlNodeData(bxmlNodePtr);
                }
            } catch (e) {
                console.log("    BXmlNode parameter: read error - " + e.message);
            }
        }

        // For other methods, try to read basic parameter data
        for (var i = 0; i < 4; i++) {
            try {
                var paramPtr = paramsPtr.add(i * Process.pointerSize).readPointer();
                if (paramPtr && !paramPtr.isNull()) {
                    console.log("    Param[" + i + "]: " + paramPtr);
                }
            } catch (e) {
                break; // No more parameters
            }
        }
    } catch (e) {
        console.log("[-] Error logging parameters: " + e.message);
    }
}

// Function to log BXmlNode data
function logBXmlNodeData(bxmlNodePtr) {
    try {
        // BXmlNode typically contains configuration data
        // Try to read some basic info
        console.log("    BXmlNode data analysis:");

        // Read first few bytes as potential string or data
        for (var i = 0; i < 64; i += 8) {
            try {
                var data = bxmlNodePtr.add(i).readU64();
                if (data.toString() !== "0") {
                    console.log("      Offset 0x" + i.toString(16) + ": " + data);
                }
            } catch (e) {
                break;
            }
        }
    } catch (e) {
        console.log("    BXmlNode: analysis error - " + e.message);
    }
}

// Function to decrypt FuzzedInt values
function decryptFuzzedInt(fuzzedIntPtr) {
    if (!fuzzedIntPtr || fuzzedIntPtr.isNull()) {
        return 0;
    }

    try {
        // FuzzedInt structure based on C# implementation
        // The actual value is XORed with coefficients
        var encryptedValue = fuzzedIntPtr.readS32();

        // Apply XOR decryption using the coefficients
        var decryptedValue = encryptedValue ^ fuzzingCoefficients.XOR1;
        decryptedValue = decryptedValue ^ fuzzingCoefficients.XOR2;

        return decryptedValue;
    } catch (e) {
        console.log("[-] Error decrypting FuzzedInt: " + e.message);
        return 0;
    }
}

// Function to trigger automatic collection
function triggerAutoCollection(instancePtr) {
    if (!instancePtr || instancePtr.isNull()) {
        return;
    }

    try {
        console.log("[+] Attempting automatic collection...");

        // Look for StartCollect method in our stored methods
        for (var methodName in goodyHutMethods) {
            if (methodName.indexOf("StartCollect") !== -1) {
                var methodInfo = goodyHutMethods[methodName];
                console.log("[*] Found StartCollect method, attempting to call...");

                // Try to invoke the method
                var il2cpp = Process.findModuleByName("libil2cpp.so");
                if (il2cpp) {
                    var il2cpp_runtime_invoke = il2cpp.findExportByName("il2cpp_runtime_invoke");
                    if (il2cpp_runtime_invoke) {
                        var invokeFunc = new NativeFunction(il2cpp_runtime_invoke, 'pointer', ['pointer', 'pointer', 'pointer', 'pointer']);
                        var result = invokeFunc(methodInfo, instancePtr, ptr(0), ptr(0));
                        console.log("[+] StartCollect invoked, result: " + result);
                    }
                }
                break;
            }
        }
    } catch (e) {
        console.log("[-] Error in automatic collection: " + e.message);
    }
}

// Function to log GoodyHutHelperConfig instance details using memory reading
function logGoodyHutHelperConfigInstance(instancePtr) {
    if (!instancePtr || instancePtr.isNull()) {
        console.log("[-] Invalid instance pointer");
        return;
    }

    console.log("=== GoodyHutHelperConfig Instance ===");
    console.log("[*] Instance pointer: " + instancePtr);

    try {
        // Read memory values directly from the instance based on C# structure
        // From the provided C# code:
        // Offset 0x10: fuzzedMaxExp (FuzzedInt)
        // Offset 0x18: fuzzedNumCitizens (FuzzedInt)
        // Offset 0x20: ageRequirement (int)
        // Offset 0x28: rampingRewards (List<RampingReward>)
        // Offset 0x30: cleanUp (bool)
        // Offset 0x34: fuzzedSellPrice (FuzzedInt)
        // Offset 0x3C: sellResource (TagEnum)
        // Offset 0x40: maxHealth (int)
        // Offset 0x44: bonusCollectible (TagEnum)
        // Offset 0x48: fuzzedBonusAmount (FuzzedInt)
        // Offset 0x50: isMystery (bool)
        // Offset 0x51: hasWeights (bool)
        // Offset 0x54: fixedRewardValue (int)

        console.log("=== Memory Layout Analysis ===");

        // Try to read basic integer fields
        try {
            var ageRequirement = instancePtr.add(0x20).readS32();
            console.log("ageRequirement (0x20): " + ageRequirement);
        } catch (e) { console.log("ageRequirement: read error - " + e.message); }

        try {
            var cleanUp = instancePtr.add(0x30).readU8();
            console.log("cleanUp (0x30): " + (cleanUp ? "true" : "false"));
        } catch (e) { console.log("cleanUp: read error - " + e.message); }

        try {
            var sellResource = instancePtr.add(0x3C).readS32();
            console.log("sellResource (0x3C): " + sellResource);
        } catch (e) { console.log("sellResource: read error - " + e.message); }

        try {
            var maxHealth = instancePtr.add(0x40).readS32();
            console.log("maxHealth (0x40): " + maxHealth);
        } catch (e) { console.log("maxHealth: read error - " + e.message); }

        try {
            var bonusCollectible = instancePtr.add(0x44).readS32();
            console.log("bonusCollectible (0x44): " + bonusCollectible);
        } catch (e) { console.log("bonusCollectible: read error - " + e.message); }

        try {
            var isMystery = instancePtr.add(0x50).readU8();
            console.log("isMystery (0x50): " + (isMystery ? "true" : "false"));
        } catch (e) { console.log("isMystery: read error - " + e.message); }

        try {
            var hasWeights = instancePtr.add(0x51).readU8();
            console.log("hasWeights (0x51): " + (hasWeights ? "true" : "false"));
        } catch (e) { console.log("hasWeights: read error - " + e.message); }

        try {
            var fixedRewardValue = instancePtr.add(0x54).readS32();
            console.log("fixedRewardValue (0x54): " + fixedRewardValue);
        } catch (e) { console.log("fixedRewardValue: read error - " + e.message); }

        // Enhanced FuzzedInt decryption for security-protected values
        console.log("=== FuzzedInt Decryption ===");
        try {
            var maxExpPtr = instancePtr.add(0x10);
            var maxExplorations = decryptFuzzedInt(maxExpPtr);
            var rawMaxExp = maxExpPtr.readS32();
            console.log("maxExplorations (0x10): " + maxExplorations + " (raw: " + rawMaxExp + ")");
        } catch (e) { console.log("maxExplorations: read error - " + e.message); }

        try {
            var numCitizensPtr = instancePtr.add(0x18);
            var numCitizens = decryptFuzzedInt(numCitizensPtr);
            var rawNumCitizens = numCitizensPtr.readS32();
            console.log("numCitizens (0x18): " + numCitizens + " (raw: " + rawNumCitizens + ")");
        } catch (e) { console.log("numCitizens: read error - " + e.message); }

        try {
            var sellPricePtr = instancePtr.add(0x34);
            var sellPrice = decryptFuzzedInt(sellPricePtr);
            var rawSellPrice = sellPricePtr.readS32();
            console.log("sellPrice (0x34): " + sellPrice + " (raw: " + rawSellPrice + ")");
        } catch (e) { console.log("sellPrice: read error - " + e.message); }

        try {
            var bonusAmountPtr = instancePtr.add(0x48);
            var bonusAmount = decryptFuzzedInt(bonusAmountPtr);
            var rawBonusAmount = bonusAmountPtr.readS32();
            console.log("bonusAmount (0x48): " + bonusAmount + " (raw: " + rawBonusAmount + ")");
        } catch (e) { console.log("bonusAmount: read error - " + e.message); }

        // Enhanced List<RampingReward> parsing at offset 0x28
        console.log("=== RampingReward Collection Analysis ===");
        try {
            var rampingRewardsPtr = instancePtr.add(0x28).readPointer();
            if (rampingRewardsPtr && !rampingRewardsPtr.isNull()) {
                console.log("rampingRewards pointer (0x28): " + rampingRewardsPtr);

                // List<T> structure in IL2CPP typically has:
                // Offset 0x10: items array pointer
                // Offset 0x18: size (count)
                // Offset 0x1C: version

                var count = rampingRewardsPtr.add(0x18).readS32();
                var itemsArrayPtr = rampingRewardsPtr.add(0x10).readPointer();

                console.log("rampingRewards count: " + count);
                console.log("rampingRewards items array: " + itemsArrayPtr);

                if (itemsArrayPtr && !itemsArrayPtr.isNull() && count > 0 && count < 100) {
                    // Parse individual RampingReward items
                    for (var i = 0; i < Math.min(count, 10); i++) { // Limit to 10 items for safety
                        try {
                            // RampingReward structure size is typically 0x14 (20 bytes)
                            // string exploration (0x0)
                            // TagEnum type (0x8)
                            // int amount (0xC)
                            // float time (0x10)

                            var rewardPtr = itemsArrayPtr.add(i * 0x14);

                            var explorationPtr = rewardPtr.readPointer();
                            var exploration = "null";
                            if (explorationPtr && !explorationPtr.isNull()) {
                                try {
                                    exploration = explorationPtr.add(0x14).readUtf16String(); // IL2CPP string offset
                                } catch (e) {
                                    exploration = "read_error";
                                }
                            }

                            var type = rewardPtr.add(0x8).readS32();
                            var amount = rewardPtr.add(0xC).readS32();
                            var time = rewardPtr.add(0x10).readFloat();

                            console.log("  Reward[" + i + "]: exploration='" + exploration +
                                       "', type=" + type + ", amount=" + amount + ", time=" + time);

                        } catch (e) {
                            console.log("  Reward[" + i + "]: parse error - " + e.message);
                        }
                    }

                    if (count > 10) {
                        console.log("  ... and " + (count - 10) + " more rewards (truncated for safety)");
                    }
                }
            } else {
                console.log("rampingRewards: null or empty list");
            }
        } catch (e) {
            console.log("rampingRewards: read error - " + e.message);
        }

    } catch (e) {
        console.log("[-] Error reading instance data: " + e.message);
    }

    console.log("=====================================");
}

// Class name validation function
function isValidGoodyHutHelperConfigClass(className) {
    if (!className) return false;

    // Exact match only - no partial matches
    return className === "GoodyHutHelperConfig";
}

// Method name validation function
function isValidGoodyHutHelperConfigMethod(methodName) {
    if (!methodName) return false;

    // Exact match against our target methods list
    for (var i = 0; i < targetMethods.length; i++) {
        if (methodName === targetMethods[i]) {
            return true;
        }
    }
    return false;
}

// Enhanced initialization with error recovery and spam filtering validation
function initializeHooksWithRetry(maxRetries) {
    var retryCount = 0;

    function attemptInitialization() {
        try {
            console.log("[*] Initializing IL2CPP hooks (attempt " + (retryCount + 1) + "/" + maxRetries + ")...");

            // Check if libil2cpp.so is available
            var il2cpp = Process.findModuleByName("libil2cpp.so");
            if (!il2cpp) {
                throw new Error("libil2cpp.so not found");
            }

            // Initialize hooks
            hookIL2CPPRuntime();
            hookMethodNameResolution();

            console.log("[+] All hooks initialized successfully!");
            console.log("[*] === FILTERING CONFIGURATION ===");
            console.log("[*] Target class: GoodyHutHelperConfig (EXACT MATCH ONLY)");
            console.log("[*] Target methods: " + targetMethods.length + " specific methods");
            console.log("[*] Unity spam filter: " + (unityEngineSpamFilter ? "ENABLED" : "DISABLED"));
            console.log("[*] Debug mode: " + (debugMode ? "ENABLED" : "DISABLED"));
            console.log("[*] Auto-collection: " + (autoCollectEnabled ? "ENABLED" : "DISABLED"));
            console.log("[*] === SPAM CLASSES FILTERED ===");
            console.log("[*] IsoBody, UIRoot, UIRect, UITweener, UIPanel, UIWidget, etc.");
            console.log("[*] Methods like FixedUpdate, Update, LateUpdate from non-GoodyHut classes");
            console.log("[*] ================================");

            return true;

        } catch (e) {
            console.log("[-] Hook initialization failed: " + e.message);
            retryCount++;

            if (retryCount < maxRetries) {
                console.log("[*] Retrying in 3 seconds...");
                setTimeout(attemptInitialization, 3000);
            } else {
                console.log("[-] Max retries reached. Hook initialization failed permanently.");
                console.log("[*] Script will continue to run but may not capture all events.");
            }

            return false;
        }
    }

    return attemptInitialization();
}

// Performance monitoring and statistics
function logPerformanceStats() {
    console.log("=== Performance Statistics ===");
    console.log("Total method calls intercepted: " + methodCallCount);
    console.log("Unique methods discovered: " + Object.keys(goodyHutMethods).length);
    console.log("Auto-collection enabled: " + autoCollectEnabled);
    console.log("Log throttle count: " + logThrottleCount);

    // List discovered methods
    if (Object.keys(goodyHutMethods).length > 0) {
        console.log("Discovered GoodyHutHelperConfig methods:");
        for (var methodName in goodyHutMethods) {
            console.log("  - " + methodName);
        }
    }

    console.log("===============================");
}

// Cleanup function for graceful shutdown
function cleanup() {
    try {
        console.log("[*] Cleaning up hooks and resources...");
        // Reset global variables
        goodyHutMethods = {};
        methodCallCount = 0;
        logThrottleCount = 0;
        console.log("[+] Cleanup completed");
    } catch (e) {
        console.log("[-] Error during cleanup: " + e.message);
    }
}

// Initialize the hooks with retry mechanism
console.log("[*] GoodyHutHelperConfig Enhanced Native IL2CPP Hook Script Started");
console.log("[*] Features: FuzzedInt decryption, RampingReward parsing, Auto-collection, Performance optimization");

// Wait for the game to load, then initialize with retries
setTimeout(function() {
    initializeHooksWithRetry(5);

    // Set up periodic performance logging
    setInterval(function() {
        if (methodCallCount > 0 && methodCallCount % 100 === 0) {
            logPerformanceStats();
        }
    }, 30000); // Every 30 seconds

}, 2000);

// Handle script termination
Process.setExceptionHandler(function(details) {
    console.log("[-] Exception caught: " + details.message);
    console.log("[-] Stack trace: " + details.stack);
    cleanup();
});

console.log("[*] GoodyHutHelperConfig Enhanced Hook Script Loaded and Ready");
